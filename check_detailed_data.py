#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
详细检查最新入库的数据
"""

import pymysql
import sys
import os
import json
from datetime import datetime

# 添加配置文件路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'dict_romte'))
from dict_romte.config import get_db_config

def check_detailed_data():
    """详细检查数据"""
    DB_CONFIG = get_db_config('default')
    
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        print("🔍 详细检查最新入库的数据...")
        
        # 获取所有数据
        cursor.execute("""
            SELECT id, INPUT_LOGIN_NO, INPUT_PROJECT_ID, RESPONSE_DATA, 
                   RETURN_CODE, RETURN_MSG, import_time
            FROM dict_zonghe_qryBillInfoByProject 
            ORDER BY id DESC
        """)
        all_data = cursor.fetchall()
        
        print(f"\n📊 总数据量: {len(all_data)} 条")
        print("=" * 80)
        
        total_bill_records = 0
        projects_with_bills = 0
        projects_without_bills = 0
        
        for i, row in enumerate(all_data, 1):
            record_id, login_no, project_id, response_data, return_code, return_msg, import_time = row
            
            print(f"\n📋 记录 {i} (ID: {record_id})")
            print(f"  项目ID: {project_id}")
            print(f"  登录用户: {login_no}")
            print(f"  返回码: {return_code}")
            print(f"  返回消息: {return_msg}")
            print(f"  导入时间: {import_time}")
            
            # 解析响应数据
            try:
                response_json = json.loads(response_data)
                out_data = response_json.get('OUT_DATA', [])
                bill_count = len(out_data)
                total_bill_records += bill_count
                
                if bill_count > 0:
                    projects_with_bills += 1
                    print(f"  💰 账单记录数: {bill_count} 条")
                    
                    # 显示账单详情
                    total_amount = 0
                    for j, bill in enumerate(out_data):
                        amount = bill.get('BILL_AMOUNT', 0)
                        total_amount += amount
                        print(f"    账单 {j+1}:")
                        print(f"      月份: {bill.get('BILL_MON', 'N/A')}")
                        print(f"      客户: {bill.get('CUST_NAME', 'N/A')}")
                        print(f"      产品: {bill.get('PROD_NAME', 'N/A')}")
                        print(f"      账户: {bill.get('ACCT_NAME', 'N/A')}")
                        print(f"      金额: {amount:,.2f} 元")
                    
                    print(f"  💵 项目总金额: {total_amount:,.2f} 元")
                else:
                    projects_without_bills += 1
                    print(f"  📝 账单记录数: 0 条 (无账单数据)")
                    
            except Exception as e:
                print(f"  ❌ 解析响应数据失败: {e}")
            
            print("  " + "-" * 60)
        
        # 统计汇总
        print(f"\n📈 数据统计汇总:")
        print(f"  总项目数: {len(all_data)}")
        print(f"  有账单项目: {projects_with_bills}")
        print(f"  无账单项目: {projects_without_bills}")
        print(f"  总账单记录数: {total_bill_records}")
        print(f"  平均每项目账单数: {total_bill_records/len(all_data):.1f}")
        
        # 检查最近的导入时间
        if all_data:
            latest_import = all_data[0][6]  # import_time
            print(f"  最新导入时间: {latest_import}")
            
            # 检查是否是最近导入的
            now = datetime.now()
            time_diff = now - latest_import
            if time_diff.total_seconds() < 300:  # 5分钟内
                print(f"  ✅ 数据是最近导入的 ({time_diff.total_seconds():.0f}秒前)")
            else:
                print(f"  ⚠️ 数据导入时间较早 ({time_diff}前)")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查数据失败: {e}")

if __name__ == "__main__":
    check_detailed_data()
