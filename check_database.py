#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
检查数据库中的账单信息数据
"""

import pymysql
import sys
import os
import json

# 添加配置文件路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'dict_romte'))
from dict_romte.config import get_db_config

def check_database():
    """检查数据库中的数据"""
    DB_CONFIG = get_db_config('default')
    
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        print("🔍 正在检查数据库...")
        
        # 检查表是否存在
        cursor.execute("SHOW TABLES LIKE 'dict_zonghe_qryBillInfoByProject'")
        table_exists = cursor.fetchone()
        
        if table_exists:
            print("✅ 表 dict_zonghe_qryBillInfoByProject 存在")
            
            # 检查表结构
            cursor.execute("DESCRIBE dict_zonghe_qryBillInfoByProject")
            columns = cursor.fetchall()
            print("\n📋 表结构:")
            for col in columns:
                print(f"  - {col[0]} ({col[1]})")
            
            # 检查数据数量
            cursor.execute("SELECT COUNT(*) FROM dict_zonghe_qryBillInfoByProject")
            count = cursor.fetchone()[0]
            print(f"\n📊 数据总数: {count} 条")
            
            if count > 0:
                # 显示最新的几条数据
                cursor.execute("""
                    SELECT INPUT_PROJECT_ID, RETURN_CODE, RETURN_MSG, 
                           LEFT(RESPONSE_DATA, 200) as RESPONSE_PREVIEW,
                           id
                    FROM dict_zonghe_qryBillInfoByProject 
                    ORDER BY id DESC LIMIT 5
                """)
                recent_data = cursor.fetchall()
                print("\n📝 最新数据:")
                for row in recent_data:
                    print(f"  ID: {row[4]}")
                    print(f"  项目ID: {row[0]}")
                    print(f"  返回码: {row[1]}")
                    print(f"  消息: {row[2]}")
                    print(f"  响应预览: {row[3]}...")
                    print("  " + "-" * 50)
                
                # 检查成功的数据
                cursor.execute("SELECT COUNT(*) FROM dict_zonghe_qryBillInfoByProject WHERE RETURN_CODE = '0'")
                success_count = cursor.fetchone()[0]
                print(f"\n✅ 成功数据: {success_count} 条")
                
                # 检查失败的数据
                cursor.execute("SELECT COUNT(*) FROM dict_zonghe_qryBillInfoByProject WHERE RETURN_CODE != '0'")
                failed_count = cursor.fetchone()[0]
                print(f"❌ 失败数据: {failed_count} 条")
                
                # 显示一条成功的完整数据示例
                if success_count > 0:
                    cursor.execute("""
                        SELECT INPUT_PROJECT_ID, RESPONSE_DATA 
                        FROM dict_zonghe_qryBillInfoByProject 
                        WHERE RETURN_CODE = '0' 
                        LIMIT 1
                    """)
                    success_data = cursor.fetchone()
                    if success_data:
                        print(f"\n🎯 成功数据示例 (项目ID: {success_data[0]}):")
                        try:
                            response_data = json.loads(success_data[1])
                            out_data = response_data.get('OUT_DATA', [])
                            print(f"  账单记录数: {len(out_data)}")
                            if out_data:
                                first_record = out_data[0]
                                print(f"  示例账单:")
                                print(f"    账单月份: {first_record.get('BILL_MON', 'N/A')}")
                                print(f"    客户名称: {first_record.get('CUST_NAME', 'N/A')}")
                                print(f"    产品名称: {first_record.get('PROD_NAME', 'N/A')}")
                                print(f"    账单金额: {first_record.get('BILL_AMOUNT', 'N/A')}")
                        except Exception as e:
                            print(f"  解析响应数据失败: {e}")
            else:
                print("\n⚠️ 表中没有数据")
                
        else:
            print("❌ 表 dict_zonghe_qryBillInfoByProject 不存在")
            
            # 检查是否有类似的表
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            print("\n📋 数据库中的所有表:")
            for table in tables:
                table_name = table[0]
                if 'bill' in table_name.lower() or 'qry' in table_name.lower() or 'dict' in table_name.lower():
                    print(f"  - {table_name} (可能相关)")
                else:
                    print(f"  - {table_name}")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")

if __name__ == "__main__":
    check_database()
