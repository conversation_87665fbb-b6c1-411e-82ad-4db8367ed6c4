# 账单信息查询爬虫 - 快速使用指南

## 🚀 快速开始

### 1. 环境准备
```bash
# 安装依赖
pip install requests pymysql urllib3

# 获取Cookie（首次使用）
python login2cookie.py
```

### 2. 选择版本运行

#### 🔧 生产环境（推荐）
```bash
python dict_zonghe_qryBillInfoByProject.py -all
```

#### ⚡ 快速处理
```bash
python dict_zonghe_qryBillInfoByProject_fast.py -all
```

#### 🚀 高性能处理
```bash
python dict_zonghe_qryBillInfoByProject_concurrent.py -all
```

## 📊 版本对比

| 版本 | 速度 | 稳定性 | 适用场景 |
|------|------|--------|----------|
| **原版** | 1.2秒/项目 | ⭐⭐⭐⭐⭐ | 生产环境 |
| **快速版** | 0.34秒/项目 | ⭐⭐⭐⭐ | 日常使用 |
| **并发版** | 0.5秒/项目 | ⭐⭐⭐ | 高性能需求 |

## 🎯 常用命令

```bash
# 处理所有项目
python dict_zonghe_qryBillInfoByProject.py -all

# 处理前20个项目
python dict_zonghe_qryBillInfoByProject.py -20

# 处理前100个项目
python dict_zonghe_qryBillInfoByProject_fast.py -100

# 并发处理前50个项目
python dict_zonghe_qryBillInfoByProject_concurrent.py -50
```

## ⚠️ 常见问题

### Cookie认证失败
```bash
# 解决方案：重新获取Cookie
python login2cookie.py
```

### 数据库连接失败
- 检查 `dict_romte/config.py` 配置
- 确认数据库服务正常

### 程序运行缓慢
- 使用快速版：`dict_zonghe_qryBillInfoByProject_fast.py`
- 检查网络连接稳定性

## 📈 性能预估

**1164个项目处理时间**：
- 原版：~23分钟
- 快速版：~7分钟  
- 并发版：~4分钟

## 🔧 配置调整

### 修改超时时间
```python
# 在代码中找到并修改
timeout=15  # 原版
timeout=8   # 快速版
```

### 修改批量大小
```python
batch_size = 50  # 快速版
batch_size = 20  # 并发版
```

## 📝 输出说明

- `[信息]` - 正常流程
- `[成功]` - 操作成功  
- `[警告]` - 需要注意
- `[错误]` - 错误信息
- `[进度]` - 处理进度

## 🎯 推荐使用

1. **日常使用**：快速版 `_fast.py`
2. **生产环境**：原版 `.py`
3. **大数据量**：并发版 `_concurrent.py`

---

💡 **提示**：首次使用前务必运行 `python login2cookie.py` 获取Cookie！
