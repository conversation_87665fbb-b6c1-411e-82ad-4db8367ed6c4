# 账单信息查询爬虫 - 技术架构说明

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户界面      │    │   爬虫程序      │    │   数据存储      │
│                 │    │                 │    │                 │
│ • 命令行参数    │───▶│ • Cookie管理    │───▶│ • MySQL数据库   │
│ • 进度显示      │    │ • API调用       │    │ • 数据表        │
│ • 结果统计      │    │ • 数据处理      │    │ • 索引优化      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   外部服务      │
                       │                 │
                       │ • dict.gmcc.net │
                       │ • API接口       │
                       │ • 认证服务      │
                       └─────────────────┘
```

## 🔧 核心组件

### 1. Cookie管理模块
```python
class CookieManager:
    - load_cookies()          # 加载Cookie
    - refresh_cookies()       # 刷新Cookie
    - get_cookies_with_retry() # 获取Cookie（带重试）
    - validate_cookies()      # 验证Cookie有效性
```

### 2. API调用模块
```python
class APIClient:
    - build_request_headers() # 构建请求头
    - build_request_data()    # 构建请求数据
    - query_data()           # 执行API查询
    - handle_response()      # 处理响应数据
```

### 3. 数据处理模块
```python
class DataProcessor:
    - get_project_ids()      # 获取项目ID列表
    - save_to_database()     # 保存数据到数据库
    - batch_insert()         # 批量插入数据
    - validate_data()        # 数据验证
```

### 4. 并发控制模块（并发版）
```python
class ConcurrentManager:
    - init_cookie_pool()     # 初始化Cookie池
    - process_batch()        # 批量处理
    - thread_worker()        # 线程工作函数
    - manage_resources()     # 资源管理
```

## 📊 数据流图

```mermaid
graph TD
    A[启动程序] --> B[解析命令行参数]
    B --> C[加载配置]
    C --> D[初始化Cookie]
    D --> E[获取项目ID列表]
    E --> F[清空原有数据]
    F --> G{选择处理模式}
    
    G -->|串行| H[串行处理]
    G -->|并发| I[并发处理]
    G -->|快速| J[快速处理]
    
    H --> K[逐个处理项目]
    I --> L[多线程处理]
    J --> M[优化处理]
    
    K --> N[API调用]
    L --> N
    M --> N
    
    N --> O[数据解析]
    O --> P[数据验证]
    P --> Q[批量保存]
    Q --> R[更新统计]
    R --> S{还有项目?}
    
    S -->|是| K
    S -->|否| T[完成处理]
    T --> U[输出结果]
```

## 🔄 处理流程

### 串行处理流程（原版/快速版）
```python
def main_process():
    1. 初始化环境
    2. 获取Cookie
    3. 获取项目列表
    4. for project in projects:
         a. 构建请求
         b. 发送API请求
         c. 处理响应
         d. 保存数据
         e. 更新进度
    5. 输出统计结果
```

### 并发处理流程（并发版）
```python
def concurrent_process():
    1. 初始化环境
    2. 初始化Cookie池
    3. 获取项目列表
    4. 分批处理:
         a. 创建线程池
         b. 分配任务到线程
         c. 并发执行API调用
         d. 收集处理结果
         e. 批量保存数据
    5. 输出统计结果
```

## 🛡️ 错误处理机制

### 1. Cookie认证错误
```python
try:
    response = api_call()
except AuthenticationError:
    refresh_cookies()
    retry_api_call()
```

### 2. 网络超时错误
```python
try:
    response = requests.post(url, timeout=15)
except TimeoutError:
    retry_with_backoff()
```

### 3. 数据库错误
```python
try:
    cursor.execute(sql)
    conn.commit()
except DatabaseError:
    conn.rollback()
    log_error()
```

## ⚡ 性能优化策略

### 1. 网络优化
- **连接复用**: 使用session对象
- **超时控制**: 合理设置timeout
- **重试机制**: 指数退避重试
- **并发控制**: 限制并发数量

### 2. 数据库优化
- **批量操作**: 使用executemany
- **事务控制**: 合理使用事务
- **连接池**: 复用数据库连接
- **索引优化**: 添加必要索引

### 3. 内存优化
- **流式处理**: 避免一次性加载大量数据
- **及时释放**: 及时关闭资源
- **缓存策略**: 合理使用缓存
- **垃圾回收**: 主动触发GC

## 🔐 安全设计

### 1. 认证安全
- Cookie加密存储
- 定期刷新认证信息
- 避免明文传输敏感信息

### 2. 数据安全
- SQL注入防护
- 数据验证和清洗
- 敏感数据脱敏

### 3. 访问控制
- 请求频率限制
- 异常访问监控
- 权限验证机制

## 📈 监控指标

### 1. 性能指标
```python
metrics = {
    'total_projects': 1164,
    'processed_projects': 1000,
    'success_rate': 95.5,
    'avg_response_time': 1.2,
    'total_duration': 1800
}
```

### 2. 错误指标
```python
errors = {
    'auth_errors': 5,
    'network_errors': 10,
    'database_errors': 2,
    'data_errors': 3
}
```

### 3. 资源指标
```python
resources = {
    'cpu_usage': 45.2,
    'memory_usage': 256,
    'network_io': 1024,
    'disk_io': 512
}
```

## 🔧 配置管理

### 1. 环境配置
```python
# config.py
DATABASE_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'username',
    'password': 'password',
    'database': 'database_name'
}

API_CONFIG = {
    'base_url': 'http://dict.gmcc.net:30722',
    'timeout': 15,
    'max_retries': 3
}

PERFORMANCE_CONFIG = {
    'batch_size': 50,
    'max_workers': 2,
    'cookie_pool_size': 3
}
```

### 2. 运行时配置
```python
# 命令行参数
parser.add_argument('-all', action='store_true')
parser.add_argument('-n', type=int, default=0)
parser.add_argument('--timeout', type=int, default=15)
parser.add_argument('--batch-size', type=int, default=50)
```

## 🚀 部署架构

### 1. 单机部署
```
┌─────────────────────────────────┐
│           服务器               │
│  ┌─────────────────────────┐   │
│  │      爬虫程序           │   │
│  │  • Python应用          │   │
│  │  • Cookie管理          │   │
│  │  • 数据处理            │   │
│  └─────────────────────────┘   │
│  ┌─────────────────────────┐   │
│  │      MySQL数据库        │   │
│  │  • 数据存储            │   │
│  │  • 索引优化            │   │
│  └─────────────────────────┘   │
└─────────────────────────────────┘
```

### 2. 分布式部署
```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│  爬虫节点1  │    │  爬虫节点2  │    │  爬虫节点N  │
│             │    │             │    │             │
│ • 任务分片  │    │ • 任务分片  │    │ • 任务分片  │
│ • 数据采集  │    │ • 数据采集  │    │ • 数据采集  │
└─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │
       └───────────────────┼───────────────────┘
                           │
                  ┌─────────────┐
                  │  数据中心   │
                  │             │
                  │ • 数据汇总  │
                  │ • 结果存储  │
                  │ • 监控告警  │
                  └─────────────┘
```

## 📋 维护清单

### 日常维护
- [ ] 监控程序运行状态
- [ ] 检查Cookie有效性
- [ ] 查看错误日志
- [ ] 验证数据完整性

### 定期维护
- [ ] 更新依赖库
- [ ] 优化数据库性能
- [ ] 清理临时文件
- [ ] 备份重要数据

### 应急处理
- [ ] Cookie失效处理
- [ ] 数据库故障恢复
- [ ] 网络异常处理
- [ ] 性能问题排查
