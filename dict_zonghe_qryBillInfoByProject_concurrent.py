#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
综合查询按项目查询账单信息爬虫程序（并发优化版）
对应接口：saleCenterApp//projectBill/qryBillInfoByProject
功能：从dict系统获取按项目查询账单信息并同步到MySQL数据库
并发优化功能：
- 多线程并发处理提升性能
- 智能Cookie池管理
- 连接池复用
- 批量数据库操作
- 实时进度监控
"""

import sys
import os
import json
import time
import requests
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
import pymysql
import subprocess
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
import queue
from collections import defaultdict

# 添加配置文件路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'dict_romte'))
from dict_romte.config import get_db_config, get_login_credentials

# 数据库配置
DB_CONFIG = get_db_config('default')

# API配置
API_URL = "http://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp//projectBill/qryBillInfoByProject"

# 并发配置
MAX_WORKERS = 2  # 最大并发线程数（保守设置避免Cookie冲突）
BATCH_SIZE = 10  # 批量处理大小
COOKIE_POOL_SIZE = 3  # Cookie池大小

# 全局变量
cookie_pool = queue.Queue()
cookie_lock = threading.Lock()
login_retry_count = 0
MAX_LOGIN_RETRIES = 3
stats_lock = threading.Lock()
global_stats = {'success': 0, 'failed': 0, 'total': 0}

def load_cookies():
    """加载Cookie"""
    try:
        with open('cookies.txt', 'r', encoding='utf-8') as f:
            cookie_data = json.load(f)

        # 检查Cookie时间戳
        timestamp_str = cookie_data.get('timestamp', '')
        if timestamp_str:
            try:
                cookie_time = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                # 如果Cookie超过2小时，认为可能过期
                if datetime.now() - cookie_time.replace(tzinfo=None) > timedelta(hours=2):
                    print(f"[警告] Cookie可能已过期 (保存时间: {timestamp_str})")
                    return None
            except Exception as e:
                print(f"[警告] 无法解析Cookie时间戳: {e}")

        cookies = {}
        for cookie in cookie_data.get('cookies', []):
            cookies[cookie['name']] = cookie['value']

        # 检查关键Cookie是否存在
        if 'JSESSIONID' not in cookies:
            print("[警告] 缺少关键Cookie: JSESSIONID")
            return None

        return cookies

    except Exception as e:
        print(f"[错误] 加载Cookie失败: {e}")
        return None

def refresh_cookies():
    """刷新Cookie - 运行login2cookie.py"""
    global login_retry_count

    if login_retry_count >= MAX_LOGIN_RETRIES:
        print(f"[错误] 已达到最大登录重试次数 ({MAX_LOGIN_RETRIES})")
        return False

    login_retry_count += 1
    print(f"[信息] 开始刷新Cookie (第 {login_retry_count} 次尝试)...")

    try:
        # 运行login2cookie.py
        result = subprocess.run([sys.executable, 'login2cookie.py'],
                              capture_output=True, text=True, timeout=300)

        if result.returncode == 0:
            print("[成功] Cookie刷新成功")
            # 重新加载Cookie
            time.sleep(2)  # 等待文件写入完成
            new_cookies = load_cookies()
            if new_cookies:
                login_retry_count = 0  # 重置重试计数
                return True
            else:
                print("[错误] Cookie刷新后仍无法加载")
                return False
        else:
            print(f"[错误] Cookie刷新失败: {result.stderr}")
            return False

    except subprocess.TimeoutExpired:
        print("[错误] Cookie刷新超时")
        return False
    except Exception as e:
        print(f"[错误] Cookie刷新异常: {e}")
        return False

def init_cookie_pool():
    """初始化Cookie池"""
    print(f"[信息] 初始化Cookie池，大小: {COOKIE_POOL_SIZE}")
    
    base_cookies = load_cookies()
    if not base_cookies:
        print("[错误] 无法加载基础Cookie")
        return False
    
    # 将相同的Cookie放入池中（实际项目中可以准备多个不同的Cookie）
    for i in range(COOKIE_POOL_SIZE):
        cookie_pool.put(base_cookies.copy())
    
    print(f"[成功] Cookie池初始化完成，包含 {cookie_pool.qsize()} 个Cookie")
    return True

def get_cookie_from_pool():
    """从Cookie池获取Cookie"""
    try:
        return cookie_pool.get(timeout=30)
    except queue.Empty:
        print("[警告] Cookie池为空，尝试刷新...")
        if refresh_cookies():
            return load_cookies()
        return None

def return_cookie_to_pool(cookies):
    """将Cookie返回到池中"""
    try:
        cookie_pool.put(cookies)
    except:
        pass  # 池满时忽略

def get_project_ids():
    """获取项目ID列表"""
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 首先尝试从视图获取
        try:
            cursor.execute("SELECT project_id FROM v_distinct_project_id LIMIT 50")
            project_ids = [row[0] for row in cursor.fetchall()]
            print(f"[信息] 从v_distinct_project_id视图获取到 {len(project_ids)} 个项目ID")
            return project_ids
        except Exception as e:
            print(f"[错误] 获取项目ID失败: {e}")
            # 如果视图不存在，尝试从其他表获取
            cursor.execute("SELECT DISTINCT `项目编码` FROM sign_data_detail WHERE `项目编码` IS NOT NULL AND `项目编码` != '' LIMIT 50")
            project_ids = [row[0] for row in cursor.fetchall()]
            print(f"[信息] 从sign_data_detail表获取到 {len(project_ids)} 个项目ID")
            return project_ids
            
    except Exception as e:
        print(f"[错误] 数据库连接失败: {e}")
        return []
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def build_request_headers(cookies):
    """构建请求头"""
    cookie_str = "; ".join([f"{name}={value}" for name, value in cookies.items()])
    
    headers = {
        'Host': "dict.gmcc.net:30722",
        'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        'Accept': "application/json, text/plain, */*",
        'Accept-Encoding': "gzip, deflate",
        'Content-Type': "application/json",
        'x-session-staffname': "dengyong",
        'x-session-regionid': "999",
        'x-session-sysusercode': "dengyong",
        'x-session-staffid': "1000032328",
        'Origin': "http://dict.gmcc.net:30722",
        'Referer': "http://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyNDA2MjcwNDMiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTI0MzAyMTI2MzAxMTQyIn0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyNDA2MjcwNDMiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTI0MzAyMTI2MzAxMTQyIn0%3D",
        'Accept-Language': "zh-CN,zh;q=0.9,ee;q=0.8",
        'Cookie': cookie_str
    }
    return headers

def build_request_data(project_id, login_no):
    """构建请求数据"""
    return {
        "ROOT": {
            "HEADER": {
                "OPR_INFO": {
                    "LOGIN_NO": login_no
                }
            },
            "BODY": project_id  # 直接使用项目ID字符串
        }
    }

def query_single_project(project_id, login_no, thread_id, max_retries=2):
    """查询单个项目（并发版本，带重试）"""
    for retry in range(max_retries + 1):
        cookies = get_cookie_from_pool()
        if not cookies:
            print(f"[错误] 线程{thread_id} 无法获取Cookie")
            time.sleep(0.5)  # 短暂等待
            continue

        try:
            headers = build_request_headers(cookies)
            data = build_request_data(project_id, login_no)

            response = requests.post(API_URL, headers=headers, json=data, timeout=15)

            if response.status_code == 200:
                result = response.json()
                return_code = result.get('ROOT', {}).get('BODY', {}).get('RETURN_CODE', '')

                if return_code == '0':
                    # 更新统计
                    with stats_lock:
                        global_stats['success'] += 1
                    print(f"[成功] 线程{thread_id} 项目 {project_id} 数据获取成功")
                    return_cookie_to_pool(cookies)
                    return result.get('ROOT', {}).get('BODY', {})
                else:
                    error_msg = result.get('ROOT', {}).get('BODY', {}).get('RETURN_MSG', '未知错误')
                    if retry < max_retries:
                        print(f"[重试] 线程{thread_id} 项目 {project_id} 第{retry+1}次重试: {error_msg}")
                        return_cookie_to_pool(cookies)
                        time.sleep(0.5)
                        continue
                    else:
                        print(f"[失败] 线程{thread_id} 项目 {project_id} 最终失败: {error_msg}")
                        with stats_lock:
                            global_stats['failed'] += 1
                        return_cookie_to_pool(cookies)
                        return None
            elif response.status_code == 401:
                # 认证失败，可能Cookie过期
                if retry < max_retries:
                    print(f"[重试] 线程{thread_id} 项目 {project_id} 认证失败，第{retry+1}次重试")
                    return_cookie_to_pool(cookies)
                    time.sleep(1)
                    continue
                else:
                    print(f"[失败] 线程{thread_id} 项目 {project_id} 认证最终失败")
                    with stats_lock:
                        global_stats['failed'] += 1
                    return_cookie_to_pool(cookies)
                    return None
            else:
                print(f"[错误] 线程{thread_id} 项目 {project_id} HTTP请求失败: {response.status_code}")
                return_cookie_to_pool(cookies)
                if retry < max_retries:
                    time.sleep(0.5)
                    continue
                else:
                    with stats_lock:
                        global_stats['failed'] += 1
                    return None

        except Exception as e:
            print(f"[错误] 线程{thread_id} 项目 {project_id} 查询异常: {e}")
            return_cookie_to_pool(cookies)
            if retry < max_retries:
                time.sleep(0.5)
                continue
            else:
                with stats_lock:
                    global_stats['failed'] += 1
                return None

    # 所有重试都失败
    with stats_lock:
        global_stats['failed'] += 1
    return None

def process_project_batch(project_batch, login_no, batch_id):
    """处理项目批次"""
    print(f"[信息] 批次{batch_id} 开始处理 {len(project_batch)} 个项目")
    
    batch_results = []
    
    with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        # 提交任务
        future_to_project = {
            executor.submit(query_single_project, project_id, login_no, f"{batch_id}-{i}"): project_id 
            for i, project_id in enumerate(project_batch)
        }
        
        # 收集结果
        for future in as_completed(future_to_project):
            project_id = future_to_project[future]
            try:
                result = future.result()
                if result:
                    batch_results.append({
                        'input_login_no': login_no,
                        'input_project_id': project_id,
                        'response_data': result
                    })
            except Exception as e:
                print(f"[错误] 批次{batch_id} 项目 {project_id} 处理失败: {e}")
    
    print(f"[完成] 批次{batch_id} 处理完成，成功 {len(batch_results)} 个项目")
    return batch_results

def save_to_database_batch(data_list):
    """批量保存数据到数据库"""
    if not data_list:
        return

    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()

        # 准备批量插入SQL
        insert_sql = """
        INSERT INTO dict_zonghe_qryBillInfoByProject (
            INPUT_LOGIN_NO, INPUT_PROJECT_ID, RESPONSE_DATA, RETURN_CODE, RETURN_MSG
        ) VALUES (%s, %s, %s, %s, %s)
        """

        # 准备批量数据
        batch_data = []
        for data in data_list:
            values = (
                data['input_login_no'],
                data['input_project_id'],
                json.dumps(data['response_data'], ensure_ascii=False),
                '0',
                'OK'
            )
            batch_data.append(values)

        # 批量插入
        cursor.executemany(insert_sql, batch_data)
        conn.commit()
        print(f"[成功] 批量保存 {len(data_list)} 条数据到数据库")

    except Exception as e:
        print(f"[错误] 批量保存数据失败: {e}")
        if 'conn' in locals():
            conn.rollback()
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def print_progress_stats():
    """打印进度统计"""
    with stats_lock:
        total = global_stats['success'] + global_stats['failed']
        success_rate = (global_stats['success'] / total * 100) if total > 0 else 0
        print(f"[统计] 总计: {total}, 成功: {global_stats['success']}, 失败: {global_stats['failed']}, 成功率: {success_rate:.1f}%")

def main(limit=None):
    """主函数（并发优化版）"""
    print("=" * 60)
    print("综合查询按项目查询账单信息爬虫程序启动（并发优化版）")
    print("=" * 60)
    print(f"[配置] 最大并发线程: {MAX_WORKERS}")
    print(f"[配置] 批处理大小: {BATCH_SIZE}")
    print(f"[配置] Cookie池大小: {COOKIE_POOL_SIZE}")

    # 初始化Cookie池
    if not init_cookie_pool():
        print("[错误] Cookie池初始化失败")
        return

    # 获取登录用户名
    login_no, _, _ = get_login_credentials()

    # 获取项目ID列表
    project_ids = get_project_ids()
    if not project_ids:
        print("[错误] 未获取到项目ID列表")
        return

    # 如果指定了限制数量，则只处理前N个项目
    if limit and limit > 0:
        project_ids = project_ids[:limit]
        print(f"[信息] 限制处理前 {limit} 个项目")

    # 初始化统计
    with stats_lock:
        global_stats['total'] = len(project_ids)

    print(f"[信息] 开始并发处理 {len(project_ids)} 个项目")

    # 清空原有数据
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        cursor.execute("DELETE FROM dict_zonghe_qryBillInfoByProject")
        conn.commit()
        print(f"[信息] 已清空原有数据")
        cursor.close()
        conn.close()
    except Exception as e:
        print(f"[警告] 清空数据失败: {e}")

    start_time = time.time()
    all_results = []

    # 分批处理
    for i in range(0, len(project_ids), BATCH_SIZE):
        batch_id = i // BATCH_SIZE + 1
        project_batch = project_ids[i:i + BATCH_SIZE]

        print(f"\n[批次] 开始处理第 {batch_id} 批，项目 {i+1}-{min(i+BATCH_SIZE, len(project_ids))}")

        # 处理当前批次
        batch_results = process_project_batch(project_batch, login_no, batch_id)

        # 立即保存当前批次结果
        if batch_results:
            save_to_database_batch(batch_results)
            all_results.extend(batch_results)

        # 打印进度统计
        print_progress_stats()

        # 批次间短暂休息
        if i + BATCH_SIZE < len(project_ids):
            time.sleep(1)

    elapsed_time = time.time() - start_time

    print(f"\n" + "=" * 60)
    print("综合查询按项目查询账单信息爬虫程序完成（并发优化版）")
    print(f"[性能] 总耗时: {elapsed_time:.1f}秒")
    print(f"[性能] 平均每项目: {elapsed_time/len(project_ids):.2f}秒")
    print(f"[结果] 成功处理: {len(all_results)} 个项目")
    print_progress_stats()
    print("=" * 60)

if __name__ == "__main__":
    if len(sys.argv) > 1:
        arg = sys.argv[1]
        if arg == "-all":
            main()
        elif arg.startswith("-") and arg[1:].isdigit():
            # 支持 -数字 格式，如 -20 表示处理前20个项目
            limit = int(arg[1:])
            main(limit)
        else:
            print("使用方法:")
            print("  python dict_zonghe_qryBillInfoByProject_concurrent.py -all     # 处理所有项目")
            print("  python dict_zonghe_qryBillInfoByProject_concurrent.py -20      # 处理前20个项目")
            print("说明: 并发优化版本，提供更快的处理速度")
            print("优化功能:")
            print("  - 多线程并发处理")
            print("  - Cookie池管理")
            print("  - 批量数据库操作")
            print("  - 实时进度监控")
    else:
        print("使用方法:")
        print("  python dict_zonghe_qryBillInfoByProject_concurrent.py -all     # 处理所有项目")
        print("  python dict_zonghe_qryBillInfoByProject_concurrent.py -20      # 处理前20个项目")
        print("说明: 并发优化版本，提供更快的处理速度")
