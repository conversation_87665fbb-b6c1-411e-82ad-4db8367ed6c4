# 综合查询按项目查询账单信息爬虫程序使用说明

## 📋 概述

本项目包含三个版本的账单信息查询爬虫程序，用于从dict系统获取按项目查询账单信息并同步到MySQL数据库。

### 🎯 功能说明
- **接口地址**: `saleCenterApp//projectBill/qryBillInfoByProject`
- **数据来源**: dict.gmcc.net:30722
- **目标数据库**: MySQL `dict_zonghe_qryBillInfoByProject` 表
- **认证方式**: Cookie认证（需要先运行 `login2cookie.py`）

## 📁 文件结构

```
├── dict_zonghe_qryBillInfoByProject.py          # 原版（稳定版）
├── dict_zonghe_qryBillInfoByProject_concurrent.py  # 并发版
├── dict_zonghe_qryBillInfoByProject_fast.py    # 快速版
├── login2cookie.py                              # Cookie获取程序
├── cookies.txt                                  # Cookie存储文件
└── dict_zonghe_qryBillInfoByProject_使用说明.md # 本说明文档
```

## 🔧 环境准备

### 依赖库安装
```bash
pip install requests pymysql urllib3
```

### 数据库配置
确保 `dict_romte/config.py` 中的数据库配置正确：
```python
DB_CONFIG = {
    'host': 'localhost',
    'user': 'your_username',
    'password': 'your_password',
    'database': 'your_database',
    'charset': 'utf8mb4'
}
```

### Cookie获取
首次使用前需要获取Cookie：
```bash
python login2cookie.py
```

## 📊 三个版本对比

| 特性 | 原版 | 并发版 | 快速版 |
|------|------|--------|--------|
| **文件名** | `dict_zonghe_qryBillInfoByProject.py` | `dict_zonghe_qryBillInfoByProject_concurrent.py` | `dict_zonghe_qryBillInfoByProject_fast.py` |
| **处理方式** | 串行处理 | 多线程并发 | 串行优化 |
| **平均耗时/项目** | ~1.2秒 | ~0.5秒 | ~0.34秒 |
| **稳定性** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ |
| **复杂度** | 简单 | 复杂 | 中等 |
| **资源占用** | 低 | 中等 | 低 |
| **适用场景** | 生产环境 | 高性能需求 | 快速处理 |

## 🚀 使用方法

### 1. 原版（推荐生产环境）

```bash
# 处理所有项目
python dict_zonghe_qryBillInfoByProject.py -all

# 处理前20个项目
python dict_zonghe_qryBillInfoByProject.py -20

# 处理前100个项目
python dict_zonghe_qryBillInfoByProject.py -100
```

**特点**：
- ✅ 稳定可靠，错误处理完善
- ✅ 自动Cookie刷新机制
- ✅ 详细的进度显示和时间预估
- ✅ 完整的日志记录

### 2. 并发版（高性能场景）

```bash
# 处理所有项目（并发）
python dict_zonghe_qryBillInfoByProject_concurrent.py -all

# 处理前50个项目（并发）
python dict_zonghe_qryBillInfoByProject_concurrent.py -50
```

**特点**：
- ⚡ 多线程并发处理（2个线程）
- 🔄 Cookie池管理
- 📦 批量数据库操作
- 📊 实时统计信息

**配置参数**：
```python
MAX_WORKERS = 2        # 最大并发线程数
BATCH_SIZE = 10        # 批量处理大小
COOKIE_POOL_SIZE = 3   # Cookie池大小
```

### 3. 快速版（快速处理）

```bash
# 处理所有项目（快速）
python dict_zonghe_qryBillInfoByProject_fast.py -all

# 处理前30个项目（快速）
python dict_zonghe_qryBillInfoByProject_fast.py -30
```

**特点**：
- ⚡ 网络超时优化（8秒）
- 💾 批量数据库操作（50条/批）
- 🔄 智能Cookie缓存（30分钟）
- 📈 实时进度和时间预估

## 🔄 程序逻辑流程

### 核心处理流程

```mermaid
graph TD
    A[启动程序] --> B[加载Cookie]
    B --> C{Cookie有效?}
    C -->|否| D[刷新Cookie]
    D --> E[重新加载Cookie]
    E --> C
    C -->|是| F[获取项目ID列表]
    F --> G[清空原有数据]
    G --> H[开始处理项目]
    H --> I[构建请求]
    I --> J[发送API请求]
    J --> K{请求成功?}
    K -->|否| L[错误处理/重试]
    L --> M{还有项目?}
    K -->|是| N[解析响应数据]
    N --> O[保存到数据库]
    O --> M
    M -->|是| H
    M -->|否| P[完成处理]
    P --> Q[显示统计结果]
```

### Cookie管理机制

```mermaid
graph LR
    A[Cookie缓存] --> B{缓存有效?}
    B -->|是| C[直接使用]
    B -->|否| D[从文件加载]
    D --> E{文件有效?}
    E -->|是| F[更新缓存]
    E -->|否| G[调用login2cookie.py]
    G --> H[重新加载]
    H --> F
    F --> C
```

## 📈 性能预估

### 处理时间预估（基于1164个项目）

| 版本 | 预估时间 | 说明 |
|------|----------|------|
| **原版** | ~23分钟 | 稳定可靠，适合生产环境 |
| **快速版** | ~7分钟 | 性能优化，推荐日常使用 |
| **并发版** | ~4分钟 | 理想情况，需要稳定网络 |

### 成功率影响因素

1. **Cookie有效性** - 最关键因素
2. **网络稳定性** - 影响请求成功率
3. **服务器负载** - 影响响应时间
4. **项目数据完整性** - 影响查询结果

## ⚠️ 注意事项

### Cookie管理
- Cookie有效期约1-2小时
- 程序会自动检测Cookie过期并刷新
- 如果自动刷新失败，需要手动运行 `login2cookie.py`

### 错误处理
- 认证失败：自动刷新Cookie重试
- 网络超时：自动重试机制
- 数据库错误：事务回滚保护

### 资源使用
- **原版**：单线程，内存占用低
- **并发版**：多线程，CPU和内存占用较高
- **快速版**：单线程优化，资源占用适中

## 🛠️ 故障排除

### 常见问题

1. **Cookie认证失败**
   ```bash
   # 手动刷新Cookie
   python login2cookie.py
   ```

2. **数据库连接失败**
   - 检查 `dict_romte/config.py` 配置
   - 确认数据库服务正常运行
   - 验证网络连接

3. **网络请求超时**
   - 检查网络连接稳定性
   - 调整超时时间设置
   - 使用快速版减少超时时间

4. **并发版Cookie冲突**
   - 减少并发线程数
   - 增加Cookie池大小
   - 使用原版或快速版替代

### 日志分析

程序输出包含以下信息：
- `[信息]` - 正常流程信息
- `[成功]` - 操作成功
- `[警告]` - 需要注意的问题
- `[错误]` - 错误信息
- `[进度]` - 处理进度
- `[统计]` - 统计信息

## 📝 数据库表结构

```sql
CREATE TABLE dict_zonghe_qryBillInfoByProject (
    id INT AUTO_INCREMENT PRIMARY KEY,
    INPUT_LOGIN_NO VARCHAR(50),
    INPUT_PROJECT_ID VARCHAR(100),
    RESPONSE_DATA TEXT,
    RETURN_CODE VARCHAR(20),
    RETURN_MSG VARCHAR(200),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 🔧 自定义配置

### 修改并发参数（并发版）
```python
# 在文件顶部修改
MAX_WORKERS = 3        # 增加并发线程数
BATCH_SIZE = 20        # 增加批处理大小
COOKIE_POOL_SIZE = 5   # 增加Cookie池大小
```

### 修改超时设置
```python
# 在query_data函数中修改
response = requests.post(API_URL, headers=headers, json=data, timeout=15)
```

### 修改批量保存大小（快速版）
```python
# 在main_fast函数中修改
batch_size = 100  # 增加批量保存大小
```

## 📞 技术支持

如有问题，请检查：
1. Cookie是否有效
2. 数据库连接是否正常
3. 网络连接是否稳定
4. 程序日志中的错误信息

建议优先使用**原版**进行生产环境处理，使用**快速版**进行日常测试和快速处理。

## 🔍 技术实现细节

### API接口规范

#### 请求格式
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "用户名"
      }
    },
    "BODY": "项目ID"
  }
}
```

#### 响应格式
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "zhengdewen",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "***************",
      "REQUEST_ID": "20250801001136409_1_69",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": [
        {
          "BILL_MON": "202506",
          "ITEM_ID": "CMGDZSICT20250407008",
          "CUST_ID": "2001303025",
          "CUST_NAME": "广东太力科技集团股份有限公司",
          "SERV_NUMBER": "53711002333",
          "PROD_ID": "9201",
          "PROD_NAME": "ICT开发集成项目",
          "ACCT_ID": "17856",
          "ACCT_NAME": "ICT云计算集成费",
          "BILL_AMOUNT": 27000.0,
          "INDB_TIME": "2025-07-03 02:01:17",
          "K_KEY": "CMGDZSICT20250407008_202506_53711002333_9201_17856"
        }
      ],
      "PROMPT_MSG": ""
    }
  }
}
```

### 关键请求头
```http
Host: dict.gmcc.net:30722
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
Accept: application/json, text/plain, */*
Content-Type: application/json
x-session-staffname: dengyong
x-session-regionid: 999
x-session-sysusercode: dengyong
x-session-staffid: 1000032328
Origin: http://dict.gmcc.net:30722
Cookie: JSESSIONID=xxx; isLogin=xxx; ...
```

## 📊 监控和调优

### 性能监控指标

1. **处理速度**
   - 每项目平均处理时间
   - 总体处理时间
   - 网络请求耗时

2. **成功率指标**
   - 整体成功率
   - 认证失败率
   - 网络错误率

3. **资源使用**
   - CPU使用率
   - 内存占用
   - 网络带宽

### 调优建议

#### 网络优化
```python
# 调整超时时间
timeout = 8  # 快速版：8秒
timeout = 15 # 原版：15秒

# 调整重试次数
max_retries = 3

# 调整请求间隔
time.sleep(0.1)  # 100ms间隔
```

#### 数据库优化
```python
# 批量插入大小
batch_size = 50   # 快速版
batch_size = 20   # 并发版

# 连接池配置
DB_CONFIG = {
    'host': 'localhost',
    'user': 'username',
    'password': 'password',
    'database': 'database',
    'charset': 'utf8mb4',
    'autocommit': True,
    'connect_timeout': 10
}
```

#### 并发优化
```python
# 并发线程数调整
MAX_WORKERS = 2  # 保守设置，避免Cookie冲突
MAX_WORKERS = 3  # 激进设置，需要稳定Cookie

# Cookie池大小
COOKIE_POOL_SIZE = 3  # 建议值
```

## 🔐 安全注意事项

### Cookie安全
- Cookie文件包含敏感认证信息
- 定期清理过期Cookie文件
- 避免在日志中输出完整Cookie

### 数据安全
- 数据库连接使用加密传输
- 敏感数据字段考虑加密存储
- 定期备份重要数据

### 访问控制
- 限制程序运行权限
- 监控异常访问行为
- 设置合理的请求频率限制

## 📋 维护清单

### 日常维护
- [ ] 检查Cookie有效性
- [ ] 监控程序运行状态
- [ ] 查看错误日志
- [ ] 验证数据完整性

### 定期维护
- [ ] 更新依赖库版本
- [ ] 清理临时文件
- [ ] 优化数据库索引
- [ ] 备份配置文件

### 故障处理
- [ ] Cookie认证失败处理流程
- [ ] 数据库连接异常处理
- [ ] 网络超时问题排查
- [ ] 数据不一致问题修复

## 📈 扩展功能建议

### 功能增强
1. **数据验证**
   - 添加数据完整性检查
   - 实现数据去重机制
   - 增加数据格式验证

2. **监控告警**
   - 添加邮件通知功能
   - 实现实时监控面板
   - 设置异常告警机制

3. **性能优化**
   - 实现智能重试策略
   - 添加缓存机制
   - 优化数据库查询

### 集成建议
1. **定时任务**
   ```bash
   # 添加到crontab
   0 2 * * * /usr/bin/python3 /path/to/dict_zonghe_qryBillInfoByProject.py -all
   ```

2. **日志管理**
   ```python
   import logging
   logging.basicConfig(
       filename='bill_query.log',
       level=logging.INFO,
       format='%(asctime)s - %(levelname)s - %(message)s'
   )
   ```

3. **配置管理**
   ```python
   # config.yaml
   api:
     url: "http://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp//projectBill/qryBillInfoByProject"
     timeout: 15

   database:
     host: "localhost"
     user: "username"
     password: "password"

   performance:
     batch_size: 50
     max_workers: 2
   ```

## 🎯 最佳实践

1. **生产环境**：使用原版，确保稳定性
2. **测试环境**：使用快速版，提高效率
3. **大数据量**：考虑使用并发版，但需要充分测试
4. **定期维护**：每周检查Cookie有效性
5. **监控告警**：设置关键指标监控
6. **数据备份**：定期备份重要数据
7. **版本控制**：使用Git管理代码变更
8. **文档更新**：及时更新使用说明和配置文档
