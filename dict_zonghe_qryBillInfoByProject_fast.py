#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
综合查询按项目查询账单信息爬虫程序（快速版）
对应接口：saleCenterApp//projectBill/qryBillInfoByProject
功能：从dict系统获取按项目查询账单信息并同步到MySQL数据库
快速优化功能：
- 减少网络延迟
- 优化数据库操作
- 智能批处理
- 连接复用
"""

import sys
import os
import json
import time
import requests
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
import pymysql
import subprocess
from datetime import datetime, timedelta

# 添加配置文件路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'dict_romte'))
from dict_romte.config import get_db_config, get_login_credentials

# 数据库配置
DB_CONFIG = get_db_config('default')

# API配置
API_URL = "http://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp//projectBill/qryBillInfoByProject"

# 全局变量
cookies_cache = None
last_cookie_refresh = None
login_retry_count = 0
MAX_LOGIN_RETRIES = 3

def load_cookies():
    """加载Cookie"""
    global cookies_cache, last_cookie_refresh
    
    try:
        with open('cookies.txt', 'r', encoding='utf-8') as f:
            cookie_data = json.load(f)

        # 检查Cookie时间戳
        timestamp_str = cookie_data.get('timestamp', '')
        if timestamp_str:
            try:
                cookie_time = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                # 如果Cookie超过1小时，认为可能过期
                if datetime.now() - cookie_time.replace(tzinfo=None) > timedelta(hours=1):
                    print(f"[警告] Cookie可能已过期 (保存时间: {timestamp_str})")
                    return None
            except Exception as e:
                print(f"[警告] 无法解析Cookie时间戳: {e}")

        cookies = {}
        for cookie in cookie_data.get('cookies', []):
            cookies[cookie['name']] = cookie['value']

        # 检查关键Cookie是否存在
        if 'JSESSIONID' not in cookies:
            print("[警告] 缺少关键Cookie: JSESSIONID")
            return None

        print(f"[信息] 成功加载 {len(cookies)} 个Cookie")
        cookies_cache = cookies
        last_cookie_refresh = datetime.now()
        return cookies

    except Exception as e:
        print(f"[错误] 加载Cookie失败: {e}")
        return None

def refresh_cookies():
    """刷新Cookie"""
    global login_retry_count

    if login_retry_count >= MAX_LOGIN_RETRIES:
        print(f"[错误] 已达到最大登录重试次数 ({MAX_LOGIN_RETRIES})")
        return False

    login_retry_count += 1
    print(f"[信息] 开始刷新Cookie (第 {login_retry_count} 次尝试)...")

    try:
        result = subprocess.run([sys.executable, 'login2cookie.py'],
                              capture_output=True, text=True, timeout=300)

        if result.returncode == 0:
            print("[成功] Cookie刷新成功")
            time.sleep(2)
            new_cookies = load_cookies()
            if new_cookies:
                login_retry_count = 0
                return True
            else:
                print("[错误] Cookie刷新后仍无法加载")
                return False
        else:
            print(f"[错误] Cookie刷新失败: {result.stderr}")
            return False

    except subprocess.TimeoutExpired:
        print("[错误] Cookie刷新超时")
        return False
    except Exception as e:
        print(f"[错误] Cookie刷新异常: {e}")
        return False

def get_cookies_with_retry():
    """获取Cookie，如果失败则自动重试"""
    global cookies_cache

    # 首先尝试使用缓存的Cookie
    if cookies_cache and last_cookie_refresh:
        # 如果缓存的Cookie不超过30分钟，直接使用
        if datetime.now() - last_cookie_refresh < timedelta(minutes=30):
            return cookies_cache

    # 尝试加载Cookie
    cookies = load_cookies()
    if cookies:
        return cookies

    # Cookie加载失败，尝试刷新
    print("[信息] Cookie无效，尝试自动刷新...")
    if refresh_cookies():
        return load_cookies()
    else:
        print("[错误] Cookie刷新失败，请手动运行 login2cookie.py")
        return None

def get_project_ids():
    """获取项目ID列表"""
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        try:
            cursor.execute("SELECT project_id FROM v_distinct_project_id")
            project_ids = [row[0] for row in cursor.fetchall()]
            print(f"[信息] 从v_distinct_project_id视图获取到 {len(project_ids)} 个项目ID")
            return project_ids
        except Exception as e:
            print(f"[错误] 获取项目ID失败: {e}")
            cursor.execute("SELECT DISTINCT `项目编码` FROM sign_data_detail WHERE `项目编码` IS NOT NULL AND `项目编码` != ''")
            project_ids = [row[0] for row in cursor.fetchall()]
            print(f"[信息] 从sign_data_detail表获取到 {len(project_ids)} 个项目ID")
            return project_ids
            
    except Exception as e:
        print(f"[错误] 数据库连接失败: {e}")
        return []
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def build_request_headers(cookies):
    """构建请求头"""
    cookie_str = "; ".join([f"{name}={value}" for name, value in cookies.items()])
    
    headers = {
        'Host': "dict.gmcc.net:30722",
        'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        'Accept': "application/json, text/plain, */*",
        'Accept-Encoding': "gzip, deflate",
        'Content-Type': "application/json",
        'x-session-staffname': "dengyong",
        'x-session-regionid': "999",
        'x-session-sysusercode': "dengyong",
        'x-session-staffid': "1000032328",
        'Origin': "http://dict.gmcc.net:30722",
        'Referer': "http://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyNDA2MjcwNDMiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTI0MzAyMTI2MzAxMTQyIn0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyNDA2MjcwNDMiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTI0MzAyMTI2MzAxMTQyIn0%3D",
        'Accept-Language': "zh-CN,zh;q=0.9,ee;q=0.8",
        'Cookie': cookie_str
    }
    return headers

def build_request_data(project_id, login_no):
    """构建请求数据"""
    return {
        "ROOT": {
            "HEADER": {
                "OPR_INFO": {
                    "LOGIN_NO": login_no
                }
            },
            "BODY": project_id
        }
    }

def query_data_fast(project_id, cookies, login_no):
    """快速查询数据"""
    try:
        headers = build_request_headers(cookies)
        data = build_request_data(project_id, login_no)

        response = requests.post(API_URL, headers=headers, json=data, timeout=8)  # 减少超时时间

        if response.status_code == 200:
            result = response.json()
            return_code = result.get('ROOT', {}).get('BODY', {}).get('RETURN_CODE', '')
            if return_code == '0':
                return result.get('ROOT', {}).get('BODY', {})
            else:
                error_msg = result.get('ROOT', {}).get('BODY', {}).get('RETURN_MSG', '未知错误')
                if '认证' in error_msg or '登录' in error_msg:
                    return 'AUTH_ERROR'  # 返回特殊标记表示认证错误
                return None
        elif response.status_code == 401:
            return 'AUTH_ERROR'
        else:
            return None

    except Exception as e:
        return None

def save_to_database_batch(data_list):
    """批量保存数据到数据库（优化版）"""
    if not data_list:
        return
    
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 使用批量插入
        insert_sql = """
        INSERT INTO dict_zonghe_qryBillInfoByProject (
            INPUT_LOGIN_NO, INPUT_PROJECT_ID, RESPONSE_DATA, RETURN_CODE, RETURN_MSG
        ) VALUES (%s, %s, %s, %s, %s)
        """
        
        batch_data = []
        for data in data_list:
            values = (
                data['input_login_no'], 
                data['input_project_id'],
                json.dumps(data['response_data'], ensure_ascii=False),
                '0',
                'OK'
            )
            batch_data.append(values)
        
        cursor.executemany(insert_sql, batch_data)
        conn.commit()
        print(f"[成功] 批量保存 {len(data_list)} 条数据")
        
    except Exception as e:
        print(f"[错误] 批量保存数据失败: {e}")
        if 'conn' in locals():
            conn.rollback()
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def main_fast(limit=None):
    """主函数（快速版）"""
    print("=" * 60)
    print("综合查询按项目查询账单信息爬虫程序启动（快速版）")
    print("=" * 60)

    # 获取Cookie
    cookies = get_cookies_with_retry()
    if not cookies:
        print("[错误] 无法获取有效Cookie")
        return

    # 获取登录用户名
    login_no, _, _ = get_login_credentials()

    # 获取项目ID列表
    project_ids = get_project_ids()
    if not project_ids:
        print("[错误] 未获取到项目ID列表")
        return

    # 如果指定了限制数量
    if limit and limit > 0:
        project_ids = project_ids[:limit]
        print(f"[信息] 限制处理前 {limit} 个项目")

    print(f"[信息] 开始快速处理 {len(project_ids)} 个项目")

    # 清空原有数据
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        cursor.execute("DELETE FROM dict_zonghe_qryBillInfoByProject")
        conn.commit()
        print(f"[信息] 已清空原有数据")
        cursor.close()
        conn.close()
    except Exception as e:
        print(f"[警告] 清空数据失败: {e}")

    start_time = time.time()
    data_list = []
    success_count = 0
    error_count = 0
    auth_error_count = 0
    batch_size = 50  # 批量保存大小

    for i, project_id in enumerate(project_ids, 1):
        print(f"[进度] {i}/{len(project_ids)} 处理项目: {project_id}")

        # 查询数据
        result = query_data_fast(project_id, cookies, login_no)

        if result == 'AUTH_ERROR':
            auth_error_count += 1
            if auth_error_count <= 3:  # 前3次认证错误时尝试刷新Cookie
                print(f"[警告] 项目 {project_id} 认证失败，尝试刷新Cookie...")
                new_cookies = get_cookies_with_retry()
                if new_cookies:
                    cookies = new_cookies
                    # 重新尝试当前项目
                    result = query_data_fast(project_id, cookies, login_no)
                    if result and result != 'AUTH_ERROR':
                        data_list.append({
                            'input_login_no': login_no,
                            'input_project_id': project_id,
                            'response_data': result
                        })
                        success_count += 1
                        print(f"[成功] 项目 {project_id} 重试成功")
                    else:
                        error_count += 1
                else:
                    error_count += 1
            else:
                error_count += 1
        elif result:
            data_list.append({
                'input_login_no': login_no,
                'input_project_id': project_id,
                'response_data': result
            })
            success_count += 1
            print(f"[成功] 项目 {project_id} 数据获取成功")
        else:
            error_count += 1

        # 批量保存数据
        if len(data_list) >= batch_size:
            save_to_database_batch(data_list)
            data_list = []

        # 显示进度和预估时间
        if i % 20 == 0:
            elapsed = time.time() - start_time
            avg_time = elapsed / i
            remaining = (len(project_ids) - i) * avg_time
            success_rate = (success_count / i * 100) if i > 0 else 0
            print(f"[统计] 已完成 {i}/{len(project_ids)}, 成功率: {success_rate:.1f}%, 预计剩余: {remaining/60:.1f}分钟")

    # 保存剩余数据
    if data_list:
        save_to_database_batch(data_list)

    elapsed_time = time.time() - start_time

    print(f"\n" + "=" * 60)
    print("综合查询按项目查询账单信息爬虫程序完成（快速版）")
    print(f"[性能] 总耗时: {elapsed_time:.1f}秒")
    print(f"[性能] 平均每项目: {elapsed_time/len(project_ids):.2f}秒")
    print(f"[结果] 成功: {success_count}, 失败: {error_count}, 认证错误: {auth_error_count}")
    if success_count + error_count > 0:
        print(f"[结果] 成功率: {success_count/(success_count+error_count)*100:.1f}%")
    print("=" * 60)

if __name__ == "__main__":
    if len(sys.argv) > 1:
        arg = sys.argv[1]
        if arg == "-all":
            main_fast()
        elif arg.startswith("-") and arg[1:].isdigit():
            limit = int(arg[1:])
            main_fast(limit)
        else:
            print("使用方法:")
            print("  python dict_zonghe_qryBillInfoByProject_fast.py -all     # 处理所有项目")
            print("  python dict_zonghe_qryBillInfoByProject_fast.py -20      # 处理前20个项目")
            print("说明: 快速优化版本，专注于性能提升")
            print("优化功能:")
            print("  - 减少网络延迟（8秒超时）")
            print("  - 智能Cookie管理")
            print("  - 批量数据库操作")
            print("  - 实时进度监控")
    else:
        print("使用方法:")
        print("  python dict_zonghe_qryBillInfoByProject_fast.py -all     # 处理所有项目")
        print("  python dict_zonghe_qryBillInfoByProject_fast.py -20      # 处理前20个项目")
        print("说明: 快速优化版本，专注于性能提升")
