#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
综合查询按项目查询账单信息爬虫程序（优化版）
对应接口：saleCenterApp//projectBill/qryBillInfoByProject
功能：从dict系统获取按项目查询账单信息并同步到MySQL数据库
优化功能：
- 自动检测Cookie过期并重新登录
- 完整的请求头和token处理
- 智能重试机制
- 更好的错误处理
"""

import sys
import os
import json
import time
import requests
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
import pymysql
import subprocess
from datetime import datetime, timedelta

# 添加配置文件路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'dict_romte'))
from dict_romte.config import get_db_config, get_login_credentials

# 数据库配置
DB_CONFIG = get_db_config('default')

# API配置
API_URL = "http://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp//projectBill/qryBillInfoByProject"

# 全局变量
cookies_cache = None
last_cookie_refresh = None
login_retry_count = 0
MAX_LOGIN_RETRIES = 3

def load_cookies():
    """加载Cookie并检查有效性"""
    global cookies_cache, last_cookie_refresh

    try:
        with open('cookies.txt', 'r', encoding='utf-8') as f:
            cookie_data = json.load(f)

        # 检查Cookie时间戳
        timestamp_str = cookie_data.get('timestamp', '')
        if timestamp_str:
            try:
                cookie_time = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                # 如果Cookie超过2小时，认为可能过期
                if datetime.now() - cookie_time.replace(tzinfo=None) > timedelta(hours=2):
                    print(f"[警告] Cookie可能已过期 (保存时间: {timestamp_str})")
                    return None
            except Exception as e:
                print(f"[警告] 无法解析Cookie时间戳: {e}")

        cookies = {}
        for cookie in cookie_data.get('cookies', []):
            cookies[cookie['name']] = cookie['value']

        # 检查关键Cookie是否存在
        if 'JSESSIONID' not in cookies:
            print("[警告] 缺少关键Cookie: JSESSIONID")
            return None

        print(f"[信息] 成功加载 {len(cookies)} 个Cookie")
        cookies_cache = cookies
        last_cookie_refresh = datetime.now()
        return cookies

    except Exception as e:
        print(f"[错误] 加载Cookie失败: {e}")
        return None

def refresh_cookies():
    """刷新Cookie - 运行login2cookie.py"""
    global login_retry_count

    if login_retry_count >= MAX_LOGIN_RETRIES:
        print(f"[错误] 已达到最大登录重试次数 ({MAX_LOGIN_RETRIES})")
        return False

    login_retry_count += 1
    print(f"[信息] 开始刷新Cookie (第 {login_retry_count} 次尝试)...")

    try:
        # 运行login2cookie.py
        result = subprocess.run([sys.executable, 'login2cookie.py'],
                              capture_output=True, text=True, timeout=300)

        if result.returncode == 0:
            print("[成功] Cookie刷新成功")
            # 重新加载Cookie
            time.sleep(2)  # 等待文件写入完成
            new_cookies = load_cookies()
            if new_cookies:
                login_retry_count = 0  # 重置重试计数
                return True
            else:
                print("[错误] Cookie刷新后仍无法加载")
                return False
        else:
            print(f"[错误] Cookie刷新失败: {result.stderr}")
            return False

    except subprocess.TimeoutExpired:
        print("[错误] Cookie刷新超时")
        return False
    except Exception as e:
        print(f"[错误] Cookie刷新异常: {e}")
        return False

def get_cookies_with_retry():
    """获取Cookie，如果失败则自动重试"""
    global cookies_cache

    # 首先尝试使用缓存的Cookie
    if cookies_cache and last_cookie_refresh:
        # 如果缓存的Cookie不超过1小时，直接使用
        if datetime.now() - last_cookie_refresh < timedelta(hours=1):
            return cookies_cache

    # 尝试加载Cookie
    cookies = load_cookies()
    if cookies:
        return cookies

    # Cookie加载失败，尝试刷新
    print("[信息] Cookie无效，尝试自动刷新...")
    if refresh_cookies():
        return load_cookies()
    else:
        print("[错误] Cookie刷新失败，请手动运行 login2cookie.py")
        return None

def get_project_ids():
    """从v_distinct_project_id视图获取项目ID列表"""
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()

        # 查询v_distinct_project_id视图（限制数量用于测试 LIMIT 50）
        cursor.execute("SELECT `project_id` FROM v_distinct_project_id ")
        project_ids = [row[0] for row in cursor.fetchall()]

        print(f"[信息] 从v_distinct_project_id视图获取到 {len(project_ids)} 个项目ID")
        return project_ids

    except Exception as e:
        print(f"[错误] 获取项目ID失败: {e}")
        # 如果视图不存在，尝试从其他表获取LIMIT 50
        try:
            cursor.execute("SELECT DISTINCT `项目编码` FROM sign_data_detail WHERE `项目编码` IS NOT NULL AND `项目编码` != '' ")
            project_ids = [row[0] for row in cursor.fetchall()]
            print(f"[信息] 从sign_data_detail表获取到 {len(project_ids)} 个项目ID")
            return project_ids
        except Exception as e2:
            print(f"[错误] 从备用表获取项目ID也失败: {e2}")
            return []
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def build_request_headers(cookies):
    """构建请求头"""
    cookie_str = "; ".join([f"{name}={value}" for name, value in cookies.items()])
    


    headers = {
        'Host': "dict.gmcc.net:30722",
        'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        'Accept': "application/json, text/plain, */*",
        'Accept-Encoding': "gzip, deflate",
        'Content-Type': "application/json",
        'x-session-staffname': "dengyong",
        'x-session-regionid': "999",
        'x-session-sysusercode': "dengyong",
        'x-session-staffid': "1000032328",
        'Origin': "http://dict.gmcc.net:30722",
        'Referer': "http://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyNDA2MjcwNDMiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTI0MzAyMTI2MzAxMTQyIn0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyNDA2MjcwNDMiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTI0MzAyMTI2MzAxMTQyIn0%3D",
        'Accept-Language': "zh-CN,zh;q=0.9,ee;q=0.8",
        'Cookie': cookie_str
    }




    return headers

def build_request_data(project_id, login_no):
    """构建请求数据 - 根据抓包结果修正格式"""
    return {
        "ROOT": {
            "HEADER": {
                "OPR_INFO": {
                    "LOGIN_NO": login_no
                }
            },
            "BODY": project_id  # 直接使用项目ID字符串，不是对象
        }
    }

def query_data(project_id, cookies, login_no, retry_count=0):
    """查询数据（带重试机制）"""
    max_retries = 2

    try:
        headers = build_request_headers(cookies)
        data = build_request_data(project_id, login_no)

        response = requests.post(API_URL, headers=headers, json=data, timeout=30)

        if response.status_code == 401:
            # 401错误通常表示认证失败，Cookie可能过期
            print(f"[警告] 项目 {project_id} 认证失败 (401)，Cookie可能过期")
            if retry_count < max_retries:
                print(f"[重试] 尝试刷新Cookie并重试 (第 {retry_count + 1} 次)")
                new_cookies = get_cookies_with_retry()
                if new_cookies:
                    return query_data(project_id, new_cookies, login_no, retry_count + 1)
            return None

        elif response.status_code == 200:
            result = response.json()

            # 检查返回码
            return_code = result.get('ROOT', {}).get('BODY', {}).get('RETURN_CODE', '')
            if return_code == '0':
                # 返回完整的BODY数据，包括OUT_DATA
                return result.get('ROOT', {}).get('BODY', {})
            else:
                error_msg = result.get('ROOT', {}).get('BODY', {}).get('RETURN_MSG', '未知错误')

                # 检查是否是认证相关错误
                if '认证' in error_msg or '登录' in error_msg or '权限' in error_msg:
                    print(f"[警告] 项目 {project_id} 认证相关错误: {error_msg}")
                    if retry_count < max_retries:
                        print(f"[重试] 尝试刷新Cookie并重试 (第 {retry_count + 1} 次)")
                        new_cookies = get_cookies_with_retry()
                        if new_cookies:
                            return query_data(project_id, new_cookies, login_no, retry_count + 1)
                    return None
                else:
                    print(f"[警告] 项目 {project_id} 查询失败: {error_msg}")
                    # 只在第一次失败时打印完整响应
                    if retry_count == 0:
                        print(f"[调试] 完整响应: {result}")
                    return None
        else:
            print(f"[错误] 项目 {project_id} HTTP请求失败: {response.status_code}")
            if retry_count == 0:
                print(f"[调试] 响应内容: {response.text[:500]}")
            return None

    except Exception as e:
        print(f"[错误] 项目 {project_id} 查询异常: {e}")
        return None

def save_to_database(data_list):
    """保存数据到数据库"""
    if not data_list:
        print("[警告] 没有数据需要保存")
        return
    
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 清空原有数据
        cursor.execute("DELETE FROM dict_zonghe_qryBillInfoByProject")
        print(f"[信息] 已清空原有数据")
        
        # 准备插入SQL
        insert_sql = """
        INSERT INTO dict_zonghe_qryBillInfoByProject (
            INPUT_LOGIN_NO, INPUT_PROJECT_ID, RESPONSE_DATA, RETURN_CODE, RETURN_MSG
        ) VALUES (%s, %s, %s, %s, %s)
        """
        
        # 批量插入数据
        for data in data_list:
            values = (
                data['input_login_no'],
                data['input_project_id'],
                json.dumps(data['response_data'], ensure_ascii=False),
                '0',
                'OK'
            )
            cursor.execute(insert_sql, values)
        
        conn.commit()
        print(f"[成功] 成功保存 {len(data_list)} 条按项目查询账单信息数据")
        
    except Exception as e:
        print(f"[错误] 保存数据失败: {e}")
        if 'conn' in locals():
            conn.rollback()
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def main(limit=None):
    """主函数（优化版）"""
    print("=" * 60)
    print("综合查询按项目查询账单信息爬虫程序启动（优化版）")
    print("=" * 60)

    # 获取Cookie（带自动重试）
    cookies = get_cookies_with_retry()
    if not cookies:
        print("[错误] Cookie获取失败，请检查网络连接或手动运行 login2cookie.py")
        return

    # 获取登录用户名
    login_no, _, _ = get_login_credentials()

    # 获取项目ID列表
    project_ids = get_project_ids()
    if not project_ids:
        print("[错误] 未获取到项目ID列表")
        return

    # 如果指定了限制数量，则只处理前N个项目
    if limit and limit > 0:
        project_ids = project_ids[:limit]
        print(f"[信息] 限制处理前 {limit} 个项目")

    print(f"[信息] 开始处理 {len(project_ids)} 个项目")

    data_list = []
    success_count = 0
    error_count = 0

    for i, project_id in enumerate(project_ids, 1):
        print(f"[进度] {i}/{len(project_ids)} 处理项目: {project_id}")

        # 查询数据（带自动重试）
        result = query_data(project_id, cookies, login_no)

        if result:
            data_list.append({
                'input_login_no': login_no,
                'input_project_id': project_id,
                'response_data': result
            })
            success_count += 1
            print(f"[成功] 项目 {project_id} 数据获取成功")
        else:
            error_count += 1

        # 每处理10个项目休息一下
        if i % 10 == 0:
            time.sleep(0)

    print(f"\n[统计] 成功: {success_count}, 失败: {error_count}")

    # 保存到数据库
    if data_list:
        save_to_database(data_list)
    else:
        print("[信息] 没有数据需要保存")

    print("=" * 60)
    print("综合查询按项目查询账单信息爬虫程序完成（优化版）")
    print("=" * 60)

if __name__ == "__main__":
    if len(sys.argv) > 1:
        arg = sys.argv[1]
        if arg == "-all":
            main()
        elif arg.startswith("-") and arg[1:].isdigit():
            # 支持 -数字 格式，如 -20 表示处理前20个项目
            limit = int(arg[1:])
            main(limit)
        else:
            print("使用方法:")
            print("  python dict_zonghe_qryBillInfoByProject.py -all     # 处理所有项目")
            print("  python dict_zonghe_qryBillInfoByProject.py -20      # 处理前20个项目")
            print("说明: -all 参数表示轮询所有项目数据同步入库")
            print("      -数字 参数表示只处理前N个项目")
            print("优化功能:")
            print("  - 自动检测Cookie过期并重新登录")
            print("  - 智能重试机制")
            print("  - 更好的错误处理")
    else:
        print("使用方法:")
        print("  python dict_zonghe_qryBillInfoByProject.py -all     # 处理所有项目")
        print("  python dict_zonghe_qryBillInfoByProject.py -20      # 处理前20个项目")
        print("说明: -all 参数表示轮询所有项目数据同步入库")
        print("      -数字 参数表示只处理前N个项目")
        print("优化功能:")
        print("  - 自动检测Cookie过期并重新登录")
        print("  - 智能重试机制")
        print("  - 更好的错误处理")
